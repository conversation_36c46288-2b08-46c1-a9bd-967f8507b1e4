import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { getPlant, getDiagnosticRecords, deletePlant } from '@/services/api';
import { Plant, DiagnosticRecord, GeminiDiagnosis } from '@/types';
import { Button } from '@/components/common/Button';
import { Card } from '@/components/common/Card';
import { Spinner } from '@/components/common/Spinner';
import { PlusIcon, LeafIcon, ArrowLeftIcon, CalendarIcon } from '@/components/common/icons';
import { getBackgroundConfig } from '@/utils/backgroundImages';
import { BackgroundWrapper } from '@/components/common/BackgroundWrapper';
import { NewDiagnostic } from '../NewDiagnostic';
import { PlantTimeline } from './PlantTimeline';
import { motion, AnimatePresence } from 'framer-motion';
import { Timestamp } from 'firebase/firestore';

const DiagnosticDetailModal = ({ record, onClose }: { record: DiagnosticRecord | null, onClose: () => void }) => {
    if (!record) return null;

    // Fonction utilitaire pour convertir le timestamp en Date de manière sécurisée
    const getDateFromTimestamp = (timestamp: any): Date => {
        if (timestamp && typeof timestamp.toDate === 'function') {
            return timestamp.toDate();
        }
        if (timestamp instanceof Date) {
            return timestamp;
        }
        if (timestamp && timestamp.seconds) {
            return new Date(timestamp.seconds * 1000);
        }
        return new Date();
    };

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
                onClick={onClose}
            >
                <motion.div
                    initial={{ scale: 0.9 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0.9 }}
                    className="bg-[#1c1a31] p-6 rounded-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
                    onClick={(e) => e.stopPropagation()}
                >
                    <h2 className="text-2xl font-bold text-white mb-2">Diagnostic : {record.diagnosis.disease}</h2>
                    <p className="text-sm text-gray-400 mb-4">
                        {getDateFromTimestamp(record.timestamp).toLocaleString()}
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        {record.imageUrls.map((url, index) => (
                            <img key={index} src={url} alt={`Diagnosis image ${index + 1}`} className="rounded-lg w-full object-cover" />
                        ))}
                    </div>
                    <div className="space-y-6 text-[#E0E0E0]">
                        <div className="bg-[#2a2847] p-4 rounded-lg border border-[#3D3B5E]">
                            <p className="leading-relaxed">{record.diagnosis.description}</p>
                        </div>

                        <div>
                            <h3 className="font-bold text-white text-xl mb-4 flex items-center gap-2">
                                📋 Plan de Traitement
                            </h3>
                            <div className="space-y-3">
                                {record.diagnosis.treatmentPlan.steps.map((step, i) => (
                                    <div key={i} className="flex items-start gap-3 p-3 bg-[#2a2847] rounded-lg border border-[#3D3B5E]">
                                        <div className="flex-shrink-0 w-6 h-6 bg-[#d385f5] text-white rounded-full flex items-center justify-center text-sm font-bold">
                                            {i + 1}
                                        </div>
                                        <p className="text-[#E0E0E0] leading-relaxed">{step}</p>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div>
                            <h3 className="font-bold text-white text-xl mb-4 flex items-center gap-2">
                                🧪 Produits Recommandés
                            </h3>
                            <div className="space-y-4">
                                {record.diagnosis.treatmentPlan.recommendedProducts.map((prod, i) => (
                                    <div key={i} className="bg-[#2a2847] p-4 rounded-lg border border-[#3D3B5E]">
                                        <h4 className="font-semibold text-[#d385f5] text-lg mb-2">
                                            {typeof prod === 'string' ? prod : prod.name || 'Produit non spécifié'}
                                        </h4>
                                        {typeof prod === 'object' && prod.type && (
                                            <p className="text-sm text-[#9CA3AF] mb-3">{prod.type}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                         <div>
                            <h3 className="font-bold text-white text-lg mb-2">Conseils de Soin Généraux</h3>
                            <ul className="list-disc list-inside space-y-1">
                                {record.diagnosis.careTips.map((tip, i) => <li key={i}>{tip}</li>)}
                            </ul>
                        </div>
                    </div>
                    <div className="text-right mt-6">
                       <Button onClick={onClose} variant="secondary">Fermer</Button>
                    </div>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

interface DiagnosticHistoryCardProps {
    record: DiagnosticRecord;
    onSelect: (record: DiagnosticRecord) => void;
}

const DiagnosticHistoryCard: React.FC<DiagnosticHistoryCardProps> = ({ record, onSelect }) => {
    const { diagnosis, timestamp, nextTreatmentDate } = record;

    // Fonction utilitaire pour convertir le timestamp en Date de manière sécurisée
    const getDateFromTimestamp = (timestamp: any): Date => {
        if (timestamp && typeof timestamp.toDate === 'function') {
            return timestamp.toDate();
        }
        if (timestamp instanceof Date) {
            return timestamp;
        }
        if (timestamp && timestamp.seconds) {
            return new Date(timestamp.seconds * 1000);
        }
        return new Date();
    };

    const getTreatmentStatus = () => {
        if (!nextTreatmentDate) return { text: 'Aucun traitement programmé', color: 'text-gray-400' };

        try {
            const now = new Date();
            const nextDate = getDateFromTimestamp(nextTreatmentDate);
            const diffTime = nextDate.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays < 0) return { text: `Traitement en retard de ${Math.abs(diffDays)} jour(s)`, color: 'text-red-400' };
            if (diffDays === 0) return { text: 'Traitement dû aujourd\'hui', color: 'text-yellow-400' };
            return { text: `Prochain traitement dans ${diffDays} jour(s)`, color: 'text-green-400' };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
            console.error('❌ Erreur lors du calcul du statut de traitement:', errorMessage);
            return { text: 'Erreur de calcul de date', color: 'text-gray-400' };
        }
    };

    const status = getTreatmentStatus();

    return (
        <Card className="w-full cursor-pointer hover:bg-[#2a2847] transition-colors" onClick={() => onSelect(record)}>
            <div className="flex flex-col h-full">
                <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                        <h3 className="font-bold text-white text-lg mb-1 line-clamp-2">{diagnosis.disease}</h3>
                        <p className="text-sm text-gray-400">{getDateFromTimestamp(timestamp).toLocaleDateString('fr-FR', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric'
                        })}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full whitespace-nowrap ml-2 ${diagnosis.isHealthy ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                        {diagnosis.isHealthy ? 'En Bonne Santé' : 'Nécessite des Soins'}
                    </span>
                </div>
                <p className="text-sm text-[#E0E0E0] line-clamp-3 flex-1">{diagnosis.description}</p>
                {nextTreatmentDate && (
                    <p className={`mt-3 text-sm font-semibold ${status.color} pt-2 border-t border-gray-700`}>
                        {status.text}
                    </p>
                )}
            </div>
        </Card>
    );
};

const PlantDetailScreen: React.FC = () => {
    const { plantId } = useParams<{ plantId: string }>();
    const navigate = useNavigate();
    const { user } = useAuth();

    const [plant, setPlant] = useState<Plant | null>(null);
    const [records, setRecords] = useState<DiagnosticRecord[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [selectedRecord, setSelectedRecord] = useState<DiagnosticRecord | null>(null);
    const [activeTab, setActiveTab] = useState<'history' | 'timeline'>('history');

    useEffect(() => {
        if (user && plantId) {
            const fetchPlantData = async () => {
                const plantData = await getPlant(user.uid, plantId);
                setPlant(plantData);
            };

            fetchPlantData();
            const unsubscribe = getDiagnosticRecords(user.uid, plantId, (fetchedRecords) => {
                setRecords(fetchedRecords);
                setIsLoading(false);
            });

            return () => unsubscribe();
        }
    }, [user, plantId]);

    const sortedRecords = useMemo(() => {
        return records.sort((a, b) => {
            // Fonction utilitaire pour obtenir le timestamp en millisecondes
            const getTimestampMillis = (timestamp: any): number => {
                if (timestamp && typeof timestamp.toMillis === 'function') {
                    return timestamp.toMillis();
                }
                if (timestamp instanceof Date) {
                    return timestamp.getTime();
                }
                if (timestamp && timestamp.seconds) {
                    return timestamp.seconds * 1000;
                }
                return 0;
            };

            return getTimestampMillis(b.timestamp) - getTimestampMillis(a.timestamp);
        });
    }, [records]);

    const handleDelete = async () => {
        if (user && plantId && window.confirm("Êtes-vous sûr de vouloir supprimer cette plante et tout son historique ? Cette action ne peut pas être annulée.")) {
            try {
                await deletePlant(user.uid, plantId);
                navigate('/');
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
                console.error("❌ Error deleting plant: ", errorMessage);
                alert("Échec de la suppression de la plante. Veuillez réessayer.");
            }
        }
    };
    
    if (isLoading) return <div className="flex items-center justify-center h-screen"><Spinner size="lg" /></div>;
    if (!plant) return <div className="text-center py-20 text-white text-2xl">Plante non trouvée.</div>;
    
    if (isAnalyzing) {
        return <NewDiagnostic plant={plant} onFinish={() => setIsAnalyzing(false)} />;
    }

    return (
        <BackgroundWrapper backgroundKey="historiqueDiagnostiques" overlayOpacity={0.75} scrollToTop={true}>
            <div className="p-4 sm:p-8">
                <div className="flex flex-col sm:flex-row justify-between items-start mb-8 gap-4">
                <div className="flex items-center gap-4">
                    <div className="p-3 rounded-full bg-gradient-to-r from-[#d385f5]/20 to-[#a364f7]/20">
                        <img
                            src={getBackgroundConfig('logo').url}
                            alt="FloraSynth Logo"
                            className="w-12 h-12 object-contain"
                            onError={(e) => {
                                // Fallback vers l'icône LeafIcon si l'image ne charge pas
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                            }}
                        />
                        <LeafIcon className="w-12 h-12 text-[#d385f5] hidden" />
                    </div>
                    <div>
                        <h1 className="text-4xl font-bold text-white">{plant.name}</h1>
                        <p className="text-[#E0E0E0]">{plant.species}</p>
                    </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                    <Button
                        onClick={() => navigate('/')}
                        variant="secondary"
                        className="flex items-center gap-2 text-[#d385f5] border-[#d385f5] hover:bg-[#d385f5]/10 focus:ring-[#d385f5]"
                    >
                        <ArrowLeftIcon className="w-4 h-4" />
                        Retour au jardin
                    </Button>
                    <Button onClick={() => setIsAnalyzing(true)}>
                        <PlusIcon className="w-5 h-5 mr-2 inline" /> Nouveau Diagnostic
                    </Button>
                     <Button onClick={handleDelete} variant="secondary" className="border-red-500 text-red-400 hover:bg-red-500/10 focus:ring-red-500">
                        Supprimer la Plante
                    </Button>
                </div>
            </div>

            {/* Onglets de navigation */}
            <div className="flex gap-4 mb-6">
                <Button
                    variant={activeTab === 'history' ? 'primary' : 'secondary'}
                    onClick={() => setActiveTab('history')}
                    className="flex items-center gap-2"
                >
                    <CalendarIcon className="w-4 h-4" />
                    Historique des Diagnostics
                </Button>
                <Button
                    variant={activeTab === 'timeline' ? 'primary' : 'secondary'}
                    onClick={() => setActiveTab('timeline')}
                    className="flex items-center gap-2"
                >
                    <LeafIcon className="w-4 h-4" />
                    Timeline d'Évolution
                </Button>
            </div>

            {/* Contenu des onglets */}
            <AnimatePresence mode="wait">
                {activeTab === 'history' && (
                    <motion.div
                        key="history"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                    >
                        <h2 className="text-2xl font-bold text-white mb-6">Historique des Diagnostics</h2>
                        {sortedRecords.length > 0 ? (
                             <motion.div
                                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                                initial="hidden"
                                animate="visible"
                                variants={{
                                    visible: { transition: { staggerChildren: 0.1 } }
                                }}
                             >
                                {sortedRecords.map(record => (
                                    <DiagnosticHistoryCard key={record.id} record={record} onSelect={setSelectedRecord} />
                                ))}
                             </motion.div>
                        ) : (
                            <div className="text-center py-16 bg-[#1c1a31] rounded-2xl">
                                <h3 className="text-xl text-white">Aucun historique pour le moment.</h3>
                                <p className="text-[#E0E0E0] mt-2">Commencez un nouveau diagnostic pour vérifier la santé de votre plante.</p>
                            </div>
                        )}
                    </motion.div>
                )}

                {activeTab === 'timeline' && (
                    <motion.div
                        key="timeline"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                    >
                        <PlantTimeline records={sortedRecords} plantName={plant?.name || 'Plante'} />
                    </motion.div>
                )}
            </AnimatePresence>

            <DiagnosticDetailModal record={selectedRecord} onClose={() => setSelectedRecord(null)} />
            </div>
        </BackgroundWrapper>
    );
};

export default PlantDetailScreen;