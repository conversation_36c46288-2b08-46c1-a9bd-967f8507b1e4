import React, { useState, useMemo } from 'react';
import { DiagnosticRecord } from '@/types';
import { Card } from '@/components/common/Card';
import { Button } from '@/components/common/Button';
import { motion, AnimatePresence } from 'framer-motion';
import {
    ChartBarIcon,
    PhotoIcon,
    CalendarIcon,
    TrendingUpIcon,
    TrendingDownIcon,
    HeartIcon,
    ExclamationTriangleIcon
} from '@/components/common/icons';

interface PlantTimelineProps {
    records: DiagnosticRecord[];
    plantName: string;
}

interface TimelineEntry {
    record: DiagnosticRecord;
    date: Date;
    healthStatus: 'healthy' | 'sick';
    isImprovement: boolean;
    daysSincePrevious?: number;
}

export const PlantTimeline: React.FC<PlantTimelineProps> = ({ records, plantName }) => {
    const [selectedComparison, setSelectedComparison] = useState<{before: DiagnosticRecord, after: DiagnosticRecord} | null>(null);
    const [viewMode, setViewMode] = useState<'timeline' | 'chart' | 'comparison'>('timeline');

    // Fonction utilitaire pour convertir le timestamp
    const getDateFromTimestamp = (timestamp: any): Date => {
        if (timestamp && typeof timestamp.toDate === 'function') {
            return timestamp.toDate();
        }
        if (timestamp instanceof Date) {
            return timestamp;
        }
        if (timestamp && timestamp.seconds) {
            return new Date(timestamp.seconds * 1000);
        }
        return new Date();
    };

    // Préparer les données de la timeline
    const timelineData = useMemo(() => {
        if (records.length === 0) return [];

        const sortedRecords = [...records].sort((a, b) => 
            getDateFromTimestamp(b.timestamp).getTime() - getDateFromTimestamp(a.timestamp).getTime()
        );

        const timeline: TimelineEntry[] = [];

        sortedRecords.forEach((record, index) => {
            const date = getDateFromTimestamp(record.timestamp);
            const healthStatus = record.diagnosis.isHealthy ? 'healthy' : 'sick';
            
            let isImprovement = false;
            let daysSincePrevious: number | undefined;

            if (index < sortedRecords.length - 1) {
                const previousRecord = sortedRecords[index + 1];
                const previousDate = getDateFromTimestamp(previousRecord.timestamp);
                daysSincePrevious = Math.floor((date.getTime() - previousDate.getTime()) / (1000 * 60 * 60 * 24));
                
                // Amélioration si on passe de malade à sain
                isImprovement = !previousRecord.diagnosis.isHealthy && record.diagnosis.isHealthy;
            }

            timeline.push({
                record,
                date,
                healthStatus,
                isImprovement,
                daysSincePrevious
            });
        });

        return timeline;
    }, [records]);

    // Calculer les statistiques d'évolution
    const evolutionStats = useMemo(() => {
        if (timelineData.length === 0) return null;

        const healthyCount = timelineData.filter(entry => entry.healthStatus === 'healthy').length;
        const sickCount = timelineData.length - healthyCount;
        const improvementCount = timelineData.filter(entry => entry.isImprovement).length;
        
        const healthRatio = (healthyCount / timelineData.length) * 100;
        const latestStatus = timelineData[0].healthStatus;
        
        // Tendance sur les 30 derniers jours
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const recentEntries = timelineData.filter(entry => entry.date >= thirtyDaysAgo);
        const recentHealthyCount = recentEntries.filter(entry => entry.healthStatus === 'healthy').length;
        const recentHealthRatio = recentEntries.length > 0 ? (recentHealthyCount / recentEntries.length) * 100 : 0;

        return {
            totalDiagnostics: timelineData.length,
            healthyCount,
            sickCount,
            improvementCount,
            healthRatio,
            latestStatus,
            recentHealthRatio,
            trend: recentHealthRatio > healthRatio ? 'improving' : recentHealthRatio < healthRatio ? 'declining' : 'stable'
        };
    }, [timelineData]);

    // Rendu de la timeline
    const renderTimeline = () => (
        <div className="space-y-6">
            {timelineData.map((entry, index) => (
                <motion.div
                    key={entry.record.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="relative"
                >
                    {/* Ligne de connexion */}
                    {index < timelineData.length - 1 && (
                        <div className="absolute left-6 top-16 w-0.5 h-16 bg-gradient-to-b from-[#d385f5] to-[#a364f7] opacity-30" />
                    )}
                    
                    <div className="flex items-start gap-4">
                        {/* Indicateur de statut */}
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${
                            entry.healthStatus === 'healthy' 
                                ? 'bg-green-500/20 border-green-500 text-green-400'
                                : 'bg-red-500/20 border-red-500 text-red-400'
                        }`}>
                            {entry.healthStatus === 'healthy' ? (
                                <HeartIcon className="w-6 h-6" />
                            ) : (
                                <ExclamationTriangleIcon className="w-6 h-6" />
                            )}
                        </div>

                        {/* Contenu de l'entrée */}
                        <Card className="flex-1">
                            <div className="flex flex-col sm:flex-row gap-4">
                                {/* Informations principales */}
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <h3 className="text-lg font-bold text-white">{entry.record.diagnosis.disease}</h3>
                                        {entry.isImprovement && (
                                            <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs font-semibold rounded-full">
                                                Amélioration
                                            </span>
                                        )}
                                    </div>
                                    
                                    <p className="text-sm text-gray-400 mb-2">
                                        {entry.date.toLocaleDateString('fr-FR', {
                                            day: 'numeric',
                                            month: 'long',
                                            year: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}
                                        {entry.daysSincePrevious && (
                                            <span className="ml-2 text-[#d385f5]">
                                                (+{entry.daysSincePrevious} jour{entry.daysSincePrevious > 1 ? 's' : ''})
                                            </span>
                                        )}
                                    </p>
                                    
                                    <p className="text-sm text-[#E0E0E0] mb-3 line-clamp-2">
                                        {entry.record.diagnosis.description}
                                    </p>

                                    {/* Actions de comparaison */}
                                    {index < timelineData.length - 1 && (
                                        <Button
                                            variant="secondary"
                                            size="sm"
                                            onClick={() => setSelectedComparison({
                                                before: timelineData[index + 1].record,
                                                after: entry.record
                                            })}
                                            className="text-xs"
                                        >
                                            <PhotoIcon className="w-3 h-3 mr-1" />
                                            Comparer avec précédent
                                        </Button>
                                    )}
                                </div>

                                {/* Images miniatures */}
                                {entry.record.imageUrls.length > 0 && (
                                    <div className="flex gap-2">
                                        {entry.record.imageUrls.slice(0, 2).map((url, imgIndex) => (
                                            <div key={imgIndex} className="w-16 h-16 rounded-lg overflow-hidden border border-[#3D3B5E]">
                                                <img
                                                    src={url}
                                                    alt={`Diagnostic ${imgIndex + 1}`}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                        ))}
                                        {entry.record.imageUrls.length > 2 && (
                                            <div className="w-16 h-16 rounded-lg bg-[#2a2847] border border-[#3D3B5E] flex items-center justify-center">
                                                <span className="text-xs text-[#E0E0E0]">+{entry.record.imageUrls.length - 2}</span>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </Card>
                    </div>
                </motion.div>
            ))}
        </div>
    );

    // Rendu des statistiques
    const renderStats = () => {
        if (!evolutionStats) return null;

        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <Card className="text-center">
                    <div className="text-2xl font-bold text-white mb-1">{evolutionStats.totalDiagnostics}</div>
                    <div className="text-sm text-[#E0E0E0]">Diagnostics Total</div>
                </Card>
                
                <Card className="text-center">
                    <div className="text-2xl font-bold text-green-400 mb-1">{evolutionStats.healthRatio.toFixed(0)}%</div>
                    <div className="text-sm text-[#E0E0E0]">Santé Globale</div>
                </Card>
                
                <Card className="text-center">
                    <div className="text-2xl font-bold text-blue-400 mb-1">{evolutionStats.improvementCount}</div>
                    <div className="text-sm text-[#E0E0E0]">Améliorations</div>
                </Card>
                
                <Card className="text-center">
                    <div className={`text-2xl font-bold mb-1 flex items-center justify-center gap-1 ${
                        evolutionStats.trend === 'improving' ? 'text-green-400' :
                        evolutionStats.trend === 'declining' ? 'text-red-400' : 'text-gray-400'
                    }`}>
                        {evolutionStats.trend === 'improving' ? (
                            <TrendingUpIcon className="w-6 h-6" />
                        ) : evolutionStats.trend === 'declining' ? (
                            <TrendingDownIcon className="w-6 h-6" />
                        ) : (
                            <span>→</span>
                        )}
                        {evolutionStats.recentHealthRatio.toFixed(0)}%
                    </div>
                    <div className="text-sm text-[#E0E0E0]">Tendance 30j</div>
                </Card>
            </div>
        );
    };

    if (records.length === 0) {
        return (
            <Card className="text-center py-8">
                <CalendarIcon className="w-12 h-12 text-[#d385f5] mx-auto mb-4" />
                <h3 className="text-lg font-bold text-white mb-2">Aucune donnée d'évolution</h3>
                <p className="text-[#E0E0E0]">Ajoutez des diagnostics pour voir l'évolution de {plantName}</p>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* En-tête avec boutons de vue */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 className="text-2xl font-bold text-white">Évolution de {plantName}</h2>
                
                <div className="flex gap-2">
                    <Button
                        variant={viewMode === 'timeline' ? 'primary' : 'secondary'}
                        size="sm"
                        onClick={() => setViewMode('timeline')}
                    >
                        <CalendarIcon className="w-4 h-4 mr-1" />
                        Timeline
                    </Button>
                    <Button
                        variant={viewMode === 'chart' ? 'primary' : 'secondary'}
                        size="sm"
                        onClick={() => setViewMode('chart')}
                    >
                        <ChartBarIcon className="w-4 h-4 mr-1" />
                        Graphique
                    </Button>
                </div>
            </div>

            {/* Statistiques */}
            {renderStats()}

            {/* Contenu principal */}
            {viewMode === 'timeline' && renderTimeline()}
            
            {viewMode === 'chart' && (
                <Card className="text-center py-8">
                    <ChartBarIcon className="w-12 h-12 text-[#d385f5] mx-auto mb-4" />
                    <h3 className="text-lg font-bold text-white mb-2">Graphique d'évolution</h3>
                    <p className="text-[#E0E0E0]">Fonctionnalité en cours de développement</p>
                </Card>
            )}

            {/* Modal de comparaison */}
            <AnimatePresence>
                {selectedComparison && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
                        onClick={() => setSelectedComparison(null)}
                    >
                        <motion.div
                            initial={{ scale: 0.9 }}
                            animate={{ scale: 1 }}
                            exit={{ scale: 0.9 }}
                            className="bg-[#1c1a31] p-6 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <h2 className="text-2xl font-bold text-white mb-6 text-center">Comparaison Avant/Après</h2>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Avant */}
                                <div>
                                    <h3 className="text-lg font-bold text-white mb-3 text-center">
                                        Avant - {getDateFromTimestamp(selectedComparison.before.timestamp).toLocaleDateString('fr-FR')}
                                    </h3>
                                    <div className="space-y-3">
                                        {selectedComparison.before.imageUrls.map((url, index) => (
                                            <img
                                                key={index}
                                                src={url}
                                                alt={`Avant ${index + 1}`}
                                                className="w-full h-48 object-cover rounded-lg border border-[#3D3B5E]"
                                            />
                                        ))}
                                        <div className="text-sm text-[#E0E0E0] bg-[#2a2847] p-3 rounded-lg">
                                            <strong>{selectedComparison.before.diagnosis.disease}</strong>
                                            <p className="mt-1">{selectedComparison.before.diagnosis.description}</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Après */}
                                <div>
                                    <h3 className="text-lg font-bold text-white mb-3 text-center">
                                        Après - {getDateFromTimestamp(selectedComparison.after.timestamp).toLocaleDateString('fr-FR')}
                                    </h3>
                                    <div className="space-y-3">
                                        {selectedComparison.after.imageUrls.map((url, index) => (
                                            <img
                                                key={index}
                                                src={url}
                                                alt={`Après ${index + 1}`}
                                                className="w-full h-48 object-cover rounded-lg border border-[#3D3B5E]"
                                            />
                                        ))}
                                        <div className="text-sm text-[#E0E0E0] bg-[#2a2847] p-3 rounded-lg">
                                            <strong>{selectedComparison.after.diagnosis.disease}</strong>
                                            <p className="mt-1">{selectedComparison.after.diagnosis.description}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="text-center mt-6">
                                <Button onClick={() => setSelectedComparison(null)} variant="secondary">
                                    Fermer
                                </Button>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};
